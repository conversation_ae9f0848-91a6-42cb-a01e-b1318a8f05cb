<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

echo "=== VÉRIFICATION DES DONNÉES D'AUTHENTIFICATION ===\n\n";

echo "1. Personnes dans la base :\n";
$personnes = DB::table('personne')->select('id', 'email')->get();
foreach ($personnes as $personne) {
    echo "   ID: {$personne->id} - Email: {$personne->email}\n";
}

echo "\n2. Utilisateurs dans la base :\n";
$users = DB::table('users')->select('id', 'personne_id', 'status')->get();
foreach ($users as $user) {
    echo "   User ID: {$user->id} - Personne ID: {$user->personne_id} - Status: {$user->status}\n";
}

echo "\n3. Relations personne-user :\n";
$relations = DB::table('personne')
    ->join('users', 'personne.id', '=', 'users.personne_id')
    ->select('personne.email', 'users.id as user_id', 'users.status')
    ->get();

foreach ($relations as $relation) {
    echo "   Email: {$relation->email} - User ID: {$relation->user_id} - Status: {$relation->status}\n";
}

echo "\n4. Rôles assignés :\n";
$userRoles = DB::table('user_role')->get();
if ($userRoles->count() > 0) {
    foreach ($userRoles as $userRole) {
        echo "   User ID: {$userRole->id_user} - Role ID: {$userRole->role_id}\n";
    }
} else {
    echo "   AUCUN RÔLE ASSIGNÉ !\n";
}

echo "\n5. Test de mot de <NAME_EMAIL> :\n";
$personne = DB::table('personne')->where('email', '<EMAIL>')->first();
if ($personne) {
    $user = DB::table('users')->where('personne_id', $personne->id)->first();
    if ($user) {
        $testPassword = 'password1';
        $isValid = Hash::check($testPassword, $user->pwd);
        echo "   Mot de passe '{$testPassword}' valide : " . ($isValid ? 'OUI' : 'NON') . "\n";
    } else {
        echo "   Aucun utilisateur trouvé pour cette personne\n";
    }
} else {
    echo "   Personne non trouvée\n";
}

echo "\n=== FIN DU TEST ===\n";
