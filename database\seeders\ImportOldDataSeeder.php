<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use PDO;

class ImportOldDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔄 Début de l\'importation des données de l\'ancienne base...');

        // Connexion à l'ancienne base de données
        $oldDbPath = database_path('database.sqlite');
        $oldDb = new PDO("sqlite:$oldDbPath");
        $oldDb->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        try {
            DB::beginTransaction();

            // 1. Importer les personnes (en évitant les doublons)
            $this->importPersonnes($oldDb);

            // 2. Importer les utilisateurs (en évitant les doublons)
            $this->importUsers($oldDb);

            // 3. Importer les types de réforme
            $this->importTypeReformes($oldDb);

            // 4. Importer les structures
            $this->importStructures($oldDb);

            // 5. Importer les réformes
            $this->importReformes($oldDb);

            // 6. Importer les activités
            $this->importActivites($oldDb);

            // 7. Importer les indicateurs
            $this->importIndicateurs($oldDb);

            // 8. Importer les suivis d'activités
            $this->importSuiviActivites($oldDb);

            DB::commit();
            $this->command->info('✅ Importation terminée avec succès !');

        } catch (\Exception $e) {
            DB::rollback();
            $this->command->error('❌ Erreur lors de l\'importation : ' . $e->getMessage());
            throw $e;
        }
    }

    private function importPersonnes($oldDb)
    {
        $this->command->info('📥 Importation des personnes...');

        $stmt = $oldDb->query("SELECT * FROM personne");
        $personnes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($personnes as $personne) {
            // Vérifier si la personne existe déjà (par email)
            $exists = DB::table('personne')->where('email', $personne['email'])->exists();

            if (!$exists) {
                DB::table('personne')->insert([
                    'id' => $personne['id'],
                    'nom' => $personne['nom'],
                    'prenom' => $personne['prenom'],
                    'fonction' => $personne['fonction'],
                    'tel' => $personne['tel'],
                    'email' => $personne['email'],
                    'created_at' => $personne['created_at'] ?? now(),
                    'updated_at' => $personne['updated_at'] ?? now(),
                ]);
            }
        }

        $this->command->info("✅ " . count($personnes) . " personnes traitées");
    }

    private function importUsers($oldDb)
    {
        $this->command->info('📥 Importation des utilisateurs...');

        $stmt = $oldDb->query("SELECT * FROM users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($users as $user) {
            // Vérifier si l'utilisateur existe déjà
            $exists = DB::table('users')->where('personne_id', $user['personne_id'])->exists();

            if (!$exists) {
                DB::table('users')->insert([
                    'id' => $user['id'],
                    'personne_id' => $user['personne_id'],
                    'pwd' => $user['pwd'],
                    'status' => $user['status'],
                    'created_at' => $user['created_at'] ?? now(),
                    'updated_at' => $user['updated_at'] ?? now(),
                ]);
            }
        }

        $this->command->info("✅ " . count($users) . " utilisateurs traités");
    }

    private function importTypeReformes($oldDb)
    {
        $this->command->info('📥 Importation des types de réforme...');

        $stmt = $oldDb->query("SELECT * FROM type_reforme");
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($types as $type) {
            $exists = DB::table('type_reforme')->where('id', $type['id'])->exists();

            if (!$exists) {
                DB::table('type_reforme')->insert([
                    'id' => $type['id'],
                    'lib' => $type['lib'],
                    'created_at' => $type['created_at'] ?? now(),
                    'updated_at' => $type['updated_at'] ?? now(),
                ]);
            }
        }

        $this->command->info("✅ " . count($types) . " types de réforme traités");
    }

    private function importStructures($oldDb)
    {
        $this->command->info('📥 Importation des structures...');

        $stmt = $oldDb->query("SELECT * FROM structure");
        $structures = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($structures as $structure) {
            $exists = DB::table('structure')->where('id', $structure['id'])->exists();

            if (!$exists) {
                DB::table('structure')->insert([
                    'id' => $structure['id'],
                    'lib_court' => $structure['lib_court'],
                    'lib_long' => $structure['lib_long'],
                    'responsable' => $structure['responsable'],
                    'created_at' => $structure['created_at'] ?? now(),
                    'updated_at' => $structure['updated_at'] ?? now(),
                ]);
            }
        }

        $this->command->info("✅ " . count($structures) . " structures traitées");
    }

    private function importReformes($oldDb)
    {
        $this->command->info('📥 Importation des réformes...');

        $stmt = $oldDb->query("SELECT * FROM reformes");
        $reformes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($reformes as $reforme) {
            $exists = DB::table('reformes')->where('id', $reforme['id'])->exists();

            if (!$exists) {
                DB::table('reformes')->insert([
                    'id' => $reforme['id'],
                    'titre' => $reforme['titre'],
                    'objectifs' => $reforme['objectifs'],
                    'budget' => $reforme['budget'] ?? null,
                    'date_debut' => $reforme['date_debut'],
                    'date_fin_prevue' => $reforme['date_fin_prevue'],
                    'date_fin' => $reforme['date_fin'] ?? null,
                    'statut' => $reforme['statut'],
                    'pieces_justificatifs' => $reforme['pieces_justificatifs'] ?? null,
                    'type_reforme' => $reforme['type_reforme'],
                    'created_by' => $reforme['created_by'] ?? 1,
                    'updated_by' => $reforme['updated_by'] ?? 1,
                    'created_at' => $reforme['created_at'] ?? now(),
                    'updated_at' => $reforme['updated_at'] ?? now(),
                ]);
            }
        }

        $this->command->info("✅ {$stmt->rowCount()} réformes traitées");
    }

    private function importActivites($oldDb)
    {
        $this->command->info('📥 Importation des activités...');

        $stmt = $oldDb->query("SELECT * FROM activites_reformes");
        $activites = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($activites as $activite) {
            $exists = DB::table('activites_reformes')->where('id', $activite['id'])->exists();

            if (!$exists) {
                DB::table('activites_reformes')->insert([
                    'id' => $activite['id'],
                    'reforme_id' => $activite['reforme_id'],
                    'libelle' => $activite['libelle'],
                    'date_debut' => $activite['date_debut'],
                    'date_fin_prevue' => $activite['date_fin_prevue'],
                    'date_fin' => $activite['date_fin'] ?? null,
                    'poids' => $activite['poids'],
                    'statut' => $activite['statut'],
                    'parent' => $activite['parent'] ?? null,
                    'created_by' => $activite['created_by'] ?? 1,
                    'updated_by' => $activite['updated_by'] ?? 1,
                    'structure_responsable' => $activite['structure_responsable'] ?? 1,
                    'created_at' => $activite['created_at'] ?? now(),
                    'updated_at' => $activite['updated_at'] ?? now(),
                ]);
            }
        }

        $this->command->info("✅ {$stmt->rowCount()} activités traitées");
    }

    private function importIndicateurs($oldDb)
    {
        $this->command->info('📥 Importation des indicateurs...');

        $stmt = $oldDb->query("SELECT * FROM indicateurs");
        $indicateurs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($indicateurs as $indicateur) {
            $exists = DB::table('indicateurs')->where('id', $indicateur['id'])->exists();

            if (!$exists) {
                DB::table('indicateurs')->insert([
                    'id' => $indicateur['id'],
                    'nom' => $indicateur['nom'],
                    'description' => $indicateur['description'] ?? null,
                    'unite' => $indicateur['unite'] ?? null,
                    'valeur_cible' => $indicateur['valeur_cible'] ?? null,
                    'valeur_actuelle' => $indicateur['valeur_actuelle'] ?? null,
                    'type' => $indicateur['type'] ?? 'quantitatif',
                    'frequence_maj' => $indicateur['frequence_maj'] ?? 'mensuel',
                    'created_at' => $indicateur['created_at'] ?? now(),
                    'updated_at' => $indicateur['updated_at'] ?? now(),
                ]);
            }
        }

        $this->command->info("✅ {$stmt->rowCount()} indicateurs traités");
    }

    private function importSuiviActivites($oldDb)
    {
        $this->command->info('📥 Importation des suivis d\'activités...');

        $stmt = $oldDb->query("SELECT * FROM suivi_activites");
        $suivis = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($suivis as $suivi) {
            $exists = DB::table('suivi_activites')->where('id', $suivi['id'])->exists();

            if (!$exists) {
                DB::table('suivi_activites')->insert([
                    'id' => $suivi['id'],
                    'activite_id' => $suivi['activite_id'],
                    'pourcentage_avancement' => $suivi['pourcentage_avancement'],
                    'commentaire' => $suivi['commentaire'] ?? null,
                    'date_maj' => $suivi['date_maj'],
                    'created_by' => $suivi['created_by'] ?? 1,
                    'statut' => $suivi['statut'] ?? 'en_cours',
                    'created_at' => $suivi['created_at'] ?? now(),
                    'updated_at' => $suivi['updated_at'] ?? now(),
                ]);
            }
        }

        $this->command->info("✅ {$stmt->rowCount()} suivis d\'activités traités");
    }
}
